import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@coozf/ui/components/card'
import { Button } from '@coozf/ui/components/button'
import { Input } from '@coozf/ui/components/input'
import { Label } from '@coozf/ui/components/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@coozf/ui/components/table'
import { Badge } from '@coozf/ui/components/badge'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@coozf/ui/components/alert-dialog'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@coozf/ui/components/dialog'
import { Plus, Search, Edit, Trash2, Copy } from 'lucide-react'
import { trpc } from '@/lib/trpc'
import { toast } from 'sonner'
import { Link } from '@tanstack/react-router'

const createApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(10, '应用名称不能超过10个字符'),
})

type CreateApplicationForm = z.infer<typeof createApplicationSchema>

function ApplicationsPage() {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  // 获取应用列表
  const {
    data: applicationsData,
    isLoading,
    refetch,
  } = trpc.application.list.useQuery({
    page,
    pageSize: 10,
    search: search || undefined,
  })

  // 创建应用表单
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CreateApplicationForm>({
    resolver: zodResolver(createApplicationSchema),
    defaultValues: {
      name: '',
    },
  })

  // 创建应用
  const createApplicationMutation = trpc.application.create.useMutation({
    onSuccess: () => {
      toast.success('应用创建成功')
      setIsCreateDialogOpen(false)
      reset()
      refetch()
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  // 删除应用
  const deleteApplicationMutation = trpc.application.delete.useMutation({
    onSuccess: () => {
      toast.success('应用删除成功')
      refetch()
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const handleDelete = (id: string) => {
    deleteApplicationMutation.mutate({ applicationId: id })
  }

  const onCreateSubmit = (data: CreateApplicationForm) => {
    createApplicationMutation.mutate(data)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('已复制到剪贴板')
  }

  const formatBalance = (balance: number) => {
    return `${balance.toFixed(2)} 蚁贝`
  }

  const formatTraffic = (trafficKB: number) => {
    if (trafficKB < 1024) {
      return `${trafficKB} KB`
    } else if (trafficKB < 1024 * 1024) {
      return `${(trafficKB / 1024).toFixed(2)} MB`
    } else {
      return `${(trafficKB / (1024 * 1024)).toFixed(2)} GB`
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">应用管理</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">加载中...</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">应用管理</h1>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              创建应用
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-[240px]">
            <DialogHeader>
              <DialogTitle>创建应用</DialogTitle>
              <DialogDescription>
                创建一个新的应用程序，系统将自动生成应用ID和密钥
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit(onCreateSubmit)} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  应用名称 <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="请输入应用名称"
                  maxLength={10}
                />
                {errors.name && (
                  <p className="text-sm text-destructive">{errors.name.message}</p>
                )}
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  disabled={createApplicationMutation.isPending}
                >
                  {createApplicationMutation.isPending ? '创建中...' : '创建应用'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardContent>
          {applicationsData?.items.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">还没有创建任何应用</p>
            </div>
          ) : (
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索应用名称..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>应用名称</TableHead>
                    <TableHead>应用ID</TableHead>
                    <TableHead>余额</TableHead>
                    <TableHead>流量</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {applicationsData?.items.map((app) => (
                    <TableRow key={app.id}>
                      <TableCell>
                        <div>
                          <Link to={'/apps/$id'} params={{ id: app.id }} className="font-medium">
                            {app.name}
                          </Link>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <code className="text-sm bg-muted px-2 py-1 rounded">{app.id}</code>
                          <Button variant="ghost" size="sm" onClick={() => copyToClipboard(app.id)}>
                            <Copy className="w-3 h-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{formatBalance(app.balance)}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{formatTraffic(0)}</Badge>
                      </TableCell>
                      <TableCell>{new Date(app.createdAt).toLocaleDateString('zh-CN')}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link to="/apps/$id/edit" params={{ id: app.id }}>
                              <Edit className="w-3 h-3" />
                            </Link>
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>确认删除</AlertDialogTitle>
                                <AlertDialogDescription>
                                  您确定要删除应用 "{app.name}" 吗？此操作无法撤销。
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>取消</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDelete(app.id)}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  删除
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页 */}
              {applicationsData && applicationsData.totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    共 {applicationsData.total} 个应用，第 {page} 页，共 {applicationsData.totalPages} 页
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" onClick={() => setPage(page - 1)} disabled={page <= 1}>
                      上一页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(page + 1)}
                      disabled={page >= applicationsData.totalPages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export const Route = createFileRoute('/_authenticated/apps/')({
  component: ApplicationsPage,
})
